Metadata-Version: 2.1
Name: ptyprocess
Version: 0.7.0
Summary: Run a subprocess in a pseudo terminal
Home-page: https://github.com/pexpect/ptyprocess
License: UNKNOWN
Author: <PERSON>
Author-email: <EMAIL>
Description-Content-Type: text/x-rst
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: ISC License (ISCL)
Classifier: Operating System :: POSIX
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Topic :: Terminals

Launch a subprocess in a pseudo terminal (pty), and interact with both the
process and its pty.

Sometimes, piping stdin and stdout is not enough. There might be a password
prompt that doesn't read from stdin, output that changes when it's going to a
pipe rather than a terminal, or curses-style interfaces that rely on a terminal.
If you need to automate these things, running the process in a pseudo terminal
(pty) is the answer.

Interface::

    p = PtyProcessUnicode.spawn(['python'])
    p.read(20)
    p.write('6+6\n')
    p.read(20)

