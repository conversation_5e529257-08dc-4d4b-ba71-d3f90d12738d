{"version": 3, "file": "7488.4d8124f72a0f10256f44.js?v=4d8124f72a0f10256f44", "mappings": ";;;;;;;;;;;AAAA;AACkC;AACI;AACtC;AACA;AACA,WAAW,gEAAC,YAAY,gEAAK;AAC7B;AACA;AACA,iEAAe,OAAO,EAAC;;;;;;;;;;;;;;ACJO;;AAE9B;AAC4B;AAC5B,wCAAwC,qEAAM;AAC9C;AACA;AACA,qBAAqB,oDAAM;AAC3B;AACA,6CAA6C,oDAAM,mDAAmD,oDAAM;AAC5G,kCAAkC,GAAG;AACrC;AACA,CAAC;;AAED;AACA,0CAA0C,qEAAM;AAChD;AACA,UAAU,sBAAsB;AAChC,EAAE,+EAAgB;AAClB;AACA;AACA,EAAE,8DAAG,8BAA8B,SAAS,gBAAgB,QAAQ;AACpE,CAAC;AACD,qDAAqD,qEAAM;AAC3D,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oCAAoC,qEAAM;AAC1C,YAAY,aAAa,EAAE,aAAa,EAAE,OAAO,EAAE,OAAO;AAC1D,CAAC;;AAKC;;;;;;;;;;;;;;;;;;;;;;;;ACxC4B;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AAaA;;AAE9B;AACA;AACA,0BAA0B,qEAAM;AAChC,sBAAsB,gBAAgB,KAAK;AAC3C;AACA,GAAG;AACH;AACA,2BAA2B,qEAAM;AACjC,KAAK;AACL,UAAU;AACV,gBAAgB,ysCAAysC;AACztC,kBAAkB,yuBAAyuB;AAC3vB;AACA,mCAAmC,qEAAM;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA,qBAAqB;AACrB;AACA;AACA,qBAAqB;AACrB;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,cAAc,iBAAiB,IAAI,QAAQ,mBAAmB,MAAM,KAAK,0MAA0M,mBAAmB,WAAW,qBAAqB,mKAAmK,oDAAoD,oGAAoG,KAAK,aAAa,IAAI,aAAa,IAAI,aAAa,+LAA+L,0BAA0B,IAAI,0BAA0B,IAAI,0BAA0B,oBAAoB,0BAA0B,IAAI,0BAA0B,IAAI,8CAA8C,IAAI,0BAA0B,IAAI,kCAAkC,4IAA4I,qEAAqE,sCAAsC,mCAAmC,IAAI,qEAAqE,IAAI,0BAA0B,oBAAoB,mFAAmF,KAAK,aAAa,qBAAqB,8CAA8C,IAAI,qBAAqB,IAAI,aAAa,IAAI,aAAa,IAAI,qDAAqD,sCAAsC,2CAA2C,IAAI,0BAA0B,oBAAoB,4CAA4C,0GAA0G,SAAS,KAAK,2CAA2C,IAAI,mCAAmC,IAAI,0BAA0B,IAAI,0BAA0B,IAAI,8CAA8C,qBAAqB,aAAa,oBAAoB,0CAA0C,mDAAmD,2BAA2B,4CAA4C,qDAAqD,8LAA8L,uBAAuB,IAAI,4BAA4B,IAAI,cAAc,qCAAqC,gCAAgC,yEAAyE,gDAAgD,IAAI,2BAA2B,oBAAoB,4CAA4C,KAAK,oCAAoC,IAAI,2BAA2B,IAAI,oCAAoC,sCAAsC,kBAAkB,IAAI,cAAc,qCAAqC,uBAAuB,sBAAsB,uBAAuB,uDAAuD,gDAAgD,IAAI,oCAAoC,IAAI,cAAc;AAC3hH,sBAAsB,0BAA0B;AAChD,gCAAgC,qEAAM;AACtC;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,KAAK;AACL,2BAA2B,qEAAM;AACjC;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qEAAM;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,qEAAM;AACZ,iEAAiE;AACjE;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,kCAAkC,qEAAM;AACxC;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA;AACA,OAAO;AACP;AACA,8BAA8B,qEAAM;AACpC;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA,OAAO;AACP;AACA,iCAAiC,qEAAM;AACvC;AACA;AACA,OAAO;AACP;AACA,qCAAqC,qEAAM;AAC3C;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,oCAAoC,qEAAM;AAC1C;AACA;AACA;AACA,OAAO;AACP;AACA,kCAAkC,qEAAM;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,4BAA4B,qEAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,kBAAkB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA,gBAAgB;AAChB;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,OAAO;AACP;AACA,2BAA2B,qEAAM;AACjC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,6BAA6B,qEAAM;AACnC;AACA,OAAO;AACP;AACA,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,qCAAqC,qEAAM;AAC3C;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP,4DAA4D;AAC5D,gCAAgC,qEAAM;AACtC;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO;AACP;AACA,iCAAiC,qEAAM;AACvC;AACA,OAAO;AACP;AACA,sCAAsC,qEAAM;AAC5C;AACA,OAAO;AACP,iBAAiB,0BAA0B;AAC3C,qCAAqC,qEAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,oIAAoI,gBAAgB,eAAe,2PAA2P,mQAAmQ,uVAAuV,qEAAqE,0FAA0F,cAAc,uJAAuJ;AAC5zC,oBAAoB,WAAW,2DAA2D,2BAA2B,qCAAqC,iBAAiB,kCAAkC,iBAAiB,kCAAkC,aAAa,+DAA+D,eAAe;AAC3V;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,qEAAM;AACR;AACA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,sEAAW;AAClC,uBAAuB,sEAAW;AAClC,6BAA6B,4EAAiB;AAC9C,6BAA6B,4EAAiB;AAC9C,2BAA2B,0EAAe;AAC1C,2BAA2B,0EAAe;AAC1C,qCAAqC,qEAAM,OAAO,yEAAS;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,qEAAM;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,KAAK,GAAG,mBAAmB;AACjD;AACA;AACA;AACA;AACA,cAAc,yEAAS;AACvB;AACA;AACA,OAAO;AACP,MAAM,8DAAG;AACT,MAAM;AACN;AACA,MAAM,8DAAG,oBAAoB,MAAM,eAAe,KAAK;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,QAAQ;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,8DAAG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,8DAAG;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,oEAAK;AACT;AACA;AACA;AACA;AACA,mBAAmB,yEAAS;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,wEAAS;AACrB;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,uBAAuB;AACpC;AACA;;AAEA;AACA;AACA,uEAAQ;AACR;AACA,CAAC;AAC2B;AAC5B,2BAA2B,qEAAM;AACjC,EAAE,8DAAG;AACL,EAAE,8DAAG;AACL,UAAU,kCAAkC,EAAE,yEAAS;AACvD;AACA,cAAc,+EAAiB;AAC/B;AACA,gCAAgC,2FAA4B;AAC5D;AACA;AACA;AACA;AACA;AACA,QAAQ,qEAAM;AACd;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,qDAAM;AACnC;AACA;AACA,+CAA+C,4BAA4B;AAC3E;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,EAAE,wEAAa;AACf;AACA;AACA;AACA;AACA;AACA,EAAE,iFAAmB;AACrB,CAAC;;AAED;AACiC;AACjC,2BAA2B,qEAAM;AACjC,mBAAmB,wDAAc;AACjC;AACA;AACA;AACA,SAAS,wDAAW;AACpB,CAAC;AACD,gCAAgC,qEAAM;AACtC;AACA,YAAY;AACZ,cAAc;AACd;;AAEA;AACA,YAAY;AACZ;AACA,wBAAwB;AACxB;AACA;AACA;AACA;;AAEA;AACA,wBAAwB;AACxB;;AAEA;AACA,YAAY;AACZ;AACA;;AAEA;AACA,mBAAmB;AACnB,aAAa;AACb;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ,cAAc;AACd;AACA;;AAEA;AACA,cAAc;AACd;AACA;AACA;;AAEA;AACA;AACA,cAAc,mBAAmB;AACjC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AAGE", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/khroma/dist/methods/channel.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/chunk-RZ5BOZE2.mjs", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/erDiagram-Q7BY3M3F.mjs"], "sourcesContent": ["/* IMPORT */\nimport _ from '../utils/index.js';\nimport Color from '../color/index.js';\n/* MAIN */\nconst channel = (color, channel) => {\n    return _.lang.round(Color.parse(color)[channel]);\n};\n/* EXPORT */\nexport default channel;\n", "import {\n  __name,\n  configureSvgSize,\n  log\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/rendering-util/insertElementsForSize.js\nimport { select } from \"d3\";\nvar getDiagramElement = /* @__PURE__ */ __name((id, securityLevel) => {\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  const svg = root.select(`[id=\"${id}\"]`);\n  return svg;\n}, \"getDiagramElement\");\n\n// src/rendering-util/setupViewPortForSVG.ts\nvar setupViewPortForSVG = /* @__PURE__ */ __name((svg, padding, cssDiagram, useMaxWidth) => {\n  svg.attr(\"class\", cssDiagram);\n  const { width, height, x, y } = calculateDimensionsWithPadding(svg, padding);\n  configureSvgSize(svg, height, width, useMaxWidth);\n  const viewBox = createViewBox(x, y, width, height, padding);\n  svg.attr(\"viewBox\", viewBox);\n  log.debug(`viewBox configured: ${viewBox} with padding: ${padding}`);\n}, \"setupViewPortForSVG\");\nvar calculateDimensionsWithPadding = /* @__PURE__ */ __name((svg, padding) => {\n  const bounds = svg.node()?.getBBox() || { width: 0, height: 0, x: 0, y: 0 };\n  return {\n    width: bounds.width + padding * 2,\n    height: bounds.height + padding * 2,\n    x: bounds.x,\n    y: bounds.y\n  };\n}, \"calculateDimensionsWithPadding\");\nvar createViewBox = /* @__PURE__ */ __name((x, y, width, height, padding) => {\n  return `${x - padding} ${y - padding} ${width} ${height}`;\n}, \"createViewBox\");\n\nexport {\n  getDiagramElement,\n  setupViewPortForSVG\n};\n", "import {\n  getDiagramElement,\n  setupViewPortForSVG\n} from \"./chunk-RZ5BOZE2.mjs\";\nimport {\n  getRegisteredLayoutAlgorithm,\n  render\n} from \"./chunk-TYCBKAJE.mjs\";\nimport \"./chunk-IIMUDSI4.mjs\";\nimport \"./chunk-VV3M67IP.mjs\";\nimport \"./chunk-HRU6DDCH.mjs\";\nimport \"./chunk-K557N5IZ.mjs\";\nimport \"./chunk-H2D2JQ3I.mjs\";\nimport \"./chunk-C3MQ5ANM.mjs\";\nimport {\n  getEdgeId,\n  utils_default\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __export,\n  __name,\n  clear,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  getDiagramTitle,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/er/parser/erDiagram.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [6, 8, 10, 22, 24, 26, 28, 33, 34, 35, 36, 37, 40, 43, 44, 50], $V1 = [1, 10], $V2 = [1, 11], $V3 = [1, 12], $V4 = [1, 13], $V5 = [1, 20], $V6 = [1, 21], $V7 = [1, 22], $V8 = [1, 23], $V9 = [1, 24], $Va = [1, 19], $Vb = [1, 25], $Vc = [1, 26], $Vd = [1, 18], $Ve = [1, 33], $Vf = [1, 34], $Vg = [1, 35], $Vh = [1, 36], $Vi = [1, 37], $Vj = [6, 8, 10, 13, 15, 17, 20, 21, 22, 24, 26, 28, 33, 34, 35, 36, 37, 40, 43, 44, 50, 63, 64, 65, 66, 67], $Vk = [1, 42], $Vl = [1, 43], $Vm = [1, 52], $Vn = [40, 50, 68, 69], $Vo = [1, 63], $Vp = [1, 61], $Vq = [1, 58], $Vr = [1, 62], $Vs = [1, 64], $Vt = [6, 8, 10, 13, 17, 22, 24, 26, 28, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 48, 49, 50, 63, 64, 65, 66, 67], $Vu = [63, 64, 65, 66, 67], $Vv = [1, 81], $Vw = [1, 80], $Vx = [1, 78], $Vy = [1, 79], $Vz = [6, 10, 42, 47], $VA = [6, 10, 13, 41, 42, 47, 48, 49], $VB = [1, 89], $VC = [1, 88], $VD = [1, 87], $VE = [19, 56], $VF = [1, 98], $VG = [1, 97], $VH = [19, 56, 58, 60];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"ER_DIAGRAM\": 4, \"document\": 5, \"EOF\": 6, \"line\": 7, \"SPACE\": 8, \"statement\": 9, \"NEWLINE\": 10, \"entityName\": 11, \"relSpec\": 12, \"COLON\": 13, \"role\": 14, \"STYLE_SEPARATOR\": 15, \"idList\": 16, \"BLOCK_START\": 17, \"attributes\": 18, \"BLOCK_STOP\": 19, \"SQS\": 20, \"SQE\": 21, \"title\": 22, \"title_value\": 23, \"acc_title\": 24, \"acc_title_value\": 25, \"acc_descr\": 26, \"acc_descr_value\": 27, \"acc_descr_multiline_value\": 28, \"direction\": 29, \"classDefStatement\": 30, \"classStatement\": 31, \"styleStatement\": 32, \"direction_tb\": 33, \"direction_bt\": 34, \"direction_rl\": 35, \"direction_lr\": 36, \"CLASSDEF\": 37, \"stylesOpt\": 38, \"separator\": 39, \"UNICODE_TEXT\": 40, \"STYLE_TEXT\": 41, \"COMMA\": 42, \"CLASS\": 43, \"STYLE\": 44, \"style\": 45, \"styleComponent\": 46, \"SEMI\": 47, \"NUM\": 48, \"BRKT\": 49, \"ENTITY_NAME\": 50, \"attribute\": 51, \"attributeType\": 52, \"attributeName\": 53, \"attributeKeyTypeList\": 54, \"attributeComment\": 55, \"ATTRIBUTE_WORD\": 56, \"attributeKeyType\": 57, \",\": 58, \"ATTRIBUTE_KEY\": 59, \"COMMENT\": 60, \"cardinality\": 61, \"relType\": 62, \"ZERO_OR_ONE\": 63, \"ZERO_OR_MORE\": 64, \"ONE_OR_MORE\": 65, \"ONLY_ONE\": 66, \"MD_PARENT\": 67, \"NON_IDENTIFYING\": 68, \"IDENTIFYING\": 69, \"WORD\": 70, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"ER_DIAGRAM\", 6: \"EOF\", 8: \"SPACE\", 10: \"NEWLINE\", 13: \"COLON\", 15: \"STYLE_SEPARATOR\", 17: \"BLOCK_START\", 19: \"BLOCK_STOP\", 20: \"SQS\", 21: \"SQE\", 22: \"title\", 23: \"title_value\", 24: \"acc_title\", 25: \"acc_title_value\", 26: \"acc_descr\", 27: \"acc_descr_value\", 28: \"acc_descr_multiline_value\", 33: \"direction_tb\", 34: \"direction_bt\", 35: \"direction_rl\", 36: \"direction_lr\", 37: \"CLASSDEF\", 40: \"UNICODE_TEXT\", 41: \"STYLE_TEXT\", 42: \"COMMA\", 43: \"CLASS\", 44: \"STYLE\", 47: \"SEMI\", 48: \"NUM\", 49: \"BRKT\", 50: \"ENTITY_NAME\", 56: \"ATTRIBUTE_WORD\", 58: \",\", 59: \"ATTRIBUTE_KEY\", 60: \"COMMENT\", 63: \"ZERO_OR_ONE\", 64: \"ZERO_OR_MORE\", 65: \"ONE_OR_MORE\", 66: \"ONLY_ONE\", 67: \"MD_PARENT\", 68: \"NON_IDENTIFYING\", 69: \"IDENTIFYING\", 70: \"WORD\" },\n    productions_: [0, [3, 3], [5, 0], [5, 2], [7, 2], [7, 1], [7, 1], [7, 1], [9, 5], [9, 9], [9, 7], [9, 7], [9, 4], [9, 6], [9, 3], [9, 5], [9, 1], [9, 3], [9, 7], [9, 9], [9, 6], [9, 8], [9, 4], [9, 6], [9, 2], [9, 2], [9, 2], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [29, 1], [29, 1], [29, 1], [29, 1], [30, 4], [16, 1], [16, 1], [16, 3], [16, 3], [31, 3], [32, 4], [38, 1], [38, 3], [45, 1], [45, 2], [39, 1], [39, 1], [39, 1], [46, 1], [46, 1], [46, 1], [46, 1], [11, 1], [11, 1], [18, 1], [18, 2], [51, 2], [51, 3], [51, 3], [51, 4], [52, 1], [53, 1], [54, 1], [54, 3], [57, 1], [55, 1], [12, 3], [61, 1], [61, 1], [61, 1], [61, 1], [61, 1], [62, 1], [62, 1], [14, 1], [14, 1], [14, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 1:\n          break;\n        case 2:\n          this.$ = [];\n          break;\n        case 3:\n          $$[$0 - 1].push($$[$0]);\n          this.$ = $$[$0 - 1];\n          break;\n        case 4:\n        case 5:\n          this.$ = $$[$0];\n          break;\n        case 6:\n        case 7:\n          this.$ = [];\n          break;\n        case 8:\n          yy.addEntity($$[$0 - 4]);\n          yy.addEntity($$[$0 - 2]);\n          yy.addRelationship($$[$0 - 4], $$[$0], $$[$0 - 2], $$[$0 - 3]);\n          break;\n        case 9:\n          yy.addEntity($$[$0 - 8]);\n          yy.addEntity($$[$0 - 4]);\n          yy.addRelationship($$[$0 - 8], $$[$0], $$[$0 - 4], $$[$0 - 5]);\n          yy.setClass([$$[$0 - 8]], $$[$0 - 6]);\n          yy.setClass([$$[$0 - 4]], $$[$0 - 2]);\n          break;\n        case 10:\n          yy.addEntity($$[$0 - 6]);\n          yy.addEntity($$[$0 - 2]);\n          yy.addRelationship($$[$0 - 6], $$[$0], $$[$0 - 2], $$[$0 - 3]);\n          yy.setClass([$$[$0 - 6]], $$[$0 - 4]);\n          break;\n        case 11:\n          yy.addEntity($$[$0 - 6]);\n          yy.addEntity($$[$0 - 4]);\n          yy.addRelationship($$[$0 - 6], $$[$0], $$[$0 - 4], $$[$0 - 5]);\n          yy.setClass([$$[$0 - 4]], $$[$0 - 2]);\n          break;\n        case 12:\n          yy.addEntity($$[$0 - 3]);\n          yy.addAttributes($$[$0 - 3], $$[$0 - 1]);\n          break;\n        case 13:\n          yy.addEntity($$[$0 - 5]);\n          yy.addAttributes($$[$0 - 5], $$[$0 - 1]);\n          yy.setClass([$$[$0 - 5]], $$[$0 - 3]);\n          break;\n        case 14:\n          yy.addEntity($$[$0 - 2]);\n          break;\n        case 15:\n          yy.addEntity($$[$0 - 4]);\n          yy.setClass([$$[$0 - 4]], $$[$0 - 2]);\n          break;\n        case 16:\n          yy.addEntity($$[$0]);\n          break;\n        case 17:\n          yy.addEntity($$[$0 - 2]);\n          yy.setClass([$$[$0 - 2]], $$[$0]);\n          break;\n        case 18:\n          yy.addEntity($$[$0 - 6], $$[$0 - 4]);\n          yy.addAttributes($$[$0 - 6], $$[$0 - 1]);\n          break;\n        case 19:\n          yy.addEntity($$[$0 - 8], $$[$0 - 6]);\n          yy.addAttributes($$[$0 - 8], $$[$0 - 1]);\n          yy.setClass([$$[$0 - 8]], $$[$0 - 3]);\n          break;\n        case 20:\n          yy.addEntity($$[$0 - 5], $$[$0 - 3]);\n          break;\n        case 21:\n          yy.addEntity($$[$0 - 7], $$[$0 - 5]);\n          yy.setClass([$$[$0 - 7]], $$[$0 - 2]);\n          break;\n        case 22:\n          yy.addEntity($$[$0 - 3], $$[$0 - 1]);\n          break;\n        case 23:\n          yy.addEntity($$[$0 - 5], $$[$0 - 3]);\n          yy.setClass([$$[$0 - 5]], $$[$0]);\n          break;\n        case 24:\n        case 25:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 26:\n        case 27:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 32:\n          yy.setDirection(\"TB\");\n          break;\n        case 33:\n          yy.setDirection(\"BT\");\n          break;\n        case 34:\n          yy.setDirection(\"RL\");\n          break;\n        case 35:\n          yy.setDirection(\"LR\");\n          break;\n        case 36:\n          this.$ = $$[$0 - 3];\n          yy.addClass($$[$0 - 2], $$[$0 - 1]);\n          break;\n        case 37:\n        case 38:\n        case 56:\n        case 64:\n          this.$ = [$$[$0]];\n          break;\n        case 39:\n        case 40:\n          this.$ = $$[$0 - 2].concat([$$[$0]]);\n          break;\n        case 41:\n          this.$ = $$[$0 - 2];\n          yy.setClass($$[$0 - 1], $$[$0]);\n          break;\n        case 42:\n          ;\n          this.$ = $$[$0 - 3];\n          yy.addCssStyles($$[$0 - 2], $$[$0 - 1]);\n          break;\n        case 43:\n          this.$ = [$$[$0]];\n          break;\n        case 44:\n          $$[$0 - 2].push($$[$0]);\n          this.$ = $$[$0 - 2];\n          break;\n        case 46:\n          this.$ = $$[$0 - 1] + $$[$0];\n          break;\n        case 54:\n        case 76:\n        case 77:\n          this.$ = $$[$0].replace(/\"/g, \"\");\n          break;\n        case 55:\n        case 78:\n          this.$ = $$[$0];\n          break;\n        case 57:\n          $$[$0].push($$[$0 - 1]);\n          this.$ = $$[$0];\n          break;\n        case 58:\n          this.$ = { type: $$[$0 - 1], name: $$[$0] };\n          break;\n        case 59:\n          this.$ = { type: $$[$0 - 2], name: $$[$0 - 1], keys: $$[$0] };\n          break;\n        case 60:\n          this.$ = { type: $$[$0 - 2], name: $$[$0 - 1], comment: $$[$0] };\n          break;\n        case 61:\n          this.$ = { type: $$[$0 - 3], name: $$[$0 - 2], keys: $$[$0 - 1], comment: $$[$0] };\n          break;\n        case 62:\n        case 63:\n        case 66:\n          this.$ = $$[$0];\n          break;\n        case 65:\n          $$[$0 - 2].push($$[$0]);\n          this.$ = $$[$0 - 2];\n          break;\n        case 67:\n          this.$ = $$[$0].replace(/\"/g, \"\");\n          break;\n        case 68:\n          this.$ = { cardA: $$[$0], relType: $$[$0 - 1], cardB: $$[$0 - 2] };\n          break;\n        case 69:\n          this.$ = yy.Cardinality.ZERO_OR_ONE;\n          break;\n        case 70:\n          this.$ = yy.Cardinality.ZERO_OR_MORE;\n          break;\n        case 71:\n          this.$ = yy.Cardinality.ONE_OR_MORE;\n          break;\n        case 72:\n          this.$ = yy.Cardinality.ONLY_ONE;\n          break;\n        case 73:\n          this.$ = yy.Cardinality.MD_PARENT;\n          break;\n        case 74:\n          this.$ = yy.Identification.NON_IDENTIFYING;\n          break;\n        case 75:\n          this.$ = yy.Identification.IDENTIFYING;\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: [1, 2] }, { 1: [3] }, o($V0, [2, 2], { 5: 3 }), { 6: [1, 4], 7: 5, 8: [1, 6], 9: 7, 10: [1, 8], 11: 9, 22: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 14, 30: 15, 31: 16, 32: 17, 33: $V5, 34: $V6, 35: $V7, 36: $V8, 37: $V9, 40: $Va, 43: $Vb, 44: $Vc, 50: $Vd }, o($V0, [2, 7], { 1: [2, 1] }), o($V0, [2, 3]), { 9: 27, 11: 9, 22: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 14, 30: 15, 31: 16, 32: 17, 33: $V5, 34: $V6, 35: $V7, 36: $V8, 37: $V9, 40: $Va, 43: $Vb, 44: $Vc, 50: $Vd }, o($V0, [2, 5]), o($V0, [2, 6]), o($V0, [2, 16], { 12: 28, 61: 32, 15: [1, 29], 17: [1, 30], 20: [1, 31], 63: $Ve, 64: $Vf, 65: $Vg, 66: $Vh, 67: $Vi }), { 23: [1, 38] }, { 25: [1, 39] }, { 27: [1, 40] }, o($V0, [2, 27]), o($V0, [2, 28]), o($V0, [2, 29]), o($V0, [2, 30]), o($V0, [2, 31]), o($Vj, [2, 54]), o($Vj, [2, 55]), o($V0, [2, 32]), o($V0, [2, 33]), o($V0, [2, 34]), o($V0, [2, 35]), { 16: 41, 40: $Vk, 41: $Vl }, { 16: 44, 40: $Vk, 41: $Vl }, { 16: 45, 40: $Vk, 41: $Vl }, o($V0, [2, 4]), { 11: 46, 40: $Va, 50: $Vd }, { 16: 47, 40: $Vk, 41: $Vl }, { 18: 48, 19: [1, 49], 51: 50, 52: 51, 56: $Vm }, { 11: 53, 40: $Va, 50: $Vd }, { 62: 54, 68: [1, 55], 69: [1, 56] }, o($Vn, [2, 69]), o($Vn, [2, 70]), o($Vn, [2, 71]), o($Vn, [2, 72]), o($Vn, [2, 73]), o($V0, [2, 24]), o($V0, [2, 25]), o($V0, [2, 26]), { 13: $Vo, 38: 57, 41: $Vp, 42: $Vq, 45: 59, 46: 60, 48: $Vr, 49: $Vs }, o($Vt, [2, 37]), o($Vt, [2, 38]), { 16: 65, 40: $Vk, 41: $Vl, 42: $Vq }, { 13: $Vo, 38: 66, 41: $Vp, 42: $Vq, 45: 59, 46: 60, 48: $Vr, 49: $Vs }, { 13: [1, 67], 15: [1, 68] }, o($V0, [2, 17], { 61: 32, 12: 69, 17: [1, 70], 42: $Vq, 63: $Ve, 64: $Vf, 65: $Vg, 66: $Vh, 67: $Vi }), { 19: [1, 71] }, o($V0, [2, 14]), { 18: 72, 19: [2, 56], 51: 50, 52: 51, 56: $Vm }, { 53: 73, 56: [1, 74] }, { 56: [2, 62] }, { 21: [1, 75] }, { 61: 76, 63: $Ve, 64: $Vf, 65: $Vg, 66: $Vh, 67: $Vi }, o($Vu, [2, 74]), o($Vu, [2, 75]), { 6: $Vv, 10: $Vw, 39: 77, 42: $Vx, 47: $Vy }, { 40: [1, 82], 41: [1, 83] }, o($Vz, [2, 43], { 46: 84, 13: $Vo, 41: $Vp, 48: $Vr, 49: $Vs }), o($VA, [2, 45]), o($VA, [2, 50]), o($VA, [2, 51]), o($VA, [2, 52]), o($VA, [2, 53]), o($V0, [2, 41], { 42: $Vq }), { 6: $Vv, 10: $Vw, 39: 85, 42: $Vx, 47: $Vy }, { 14: 86, 40: $VB, 50: $VC, 70: $VD }, { 16: 90, 40: $Vk, 41: $Vl }, { 11: 91, 40: $Va, 50: $Vd }, { 18: 92, 19: [1, 93], 51: 50, 52: 51, 56: $Vm }, o($V0, [2, 12]), { 19: [2, 57] }, o($VE, [2, 58], { 54: 94, 55: 95, 57: 96, 59: $VF, 60: $VG }), o([19, 56, 59, 60], [2, 63]), o($V0, [2, 22], { 15: [1, 100], 17: [1, 99] }), o([40, 50], [2, 68]), o($V0, [2, 36]), { 13: $Vo, 41: $Vp, 45: 101, 46: 60, 48: $Vr, 49: $Vs }, o($V0, [2, 47]), o($V0, [2, 48]), o($V0, [2, 49]), o($Vt, [2, 39]), o($Vt, [2, 40]), o($VA, [2, 46]), o($V0, [2, 42]), o($V0, [2, 8]), o($V0, [2, 76]), o($V0, [2, 77]), o($V0, [2, 78]), { 13: [1, 102], 42: $Vq }, { 13: [1, 104], 15: [1, 103] }, { 19: [1, 105] }, o($V0, [2, 15]), o($VE, [2, 59], { 55: 106, 58: [1, 107], 60: $VG }), o($VE, [2, 60]), o($VH, [2, 64]), o($VE, [2, 67]), o($VH, [2, 66]), { 18: 108, 19: [1, 109], 51: 50, 52: 51, 56: $Vm }, { 16: 110, 40: $Vk, 41: $Vl }, o($Vz, [2, 44], { 46: 84, 13: $Vo, 41: $Vp, 48: $Vr, 49: $Vs }), { 14: 111, 40: $VB, 50: $VC, 70: $VD }, { 16: 112, 40: $Vk, 41: $Vl }, { 14: 113, 40: $VB, 50: $VC, 70: $VD }, o($V0, [2, 13]), o($VE, [2, 61]), { 57: 114, 59: $VF }, { 19: [1, 115] }, o($V0, [2, 20]), o($V0, [2, 23], { 17: [1, 116], 42: $Vq }), o($V0, [2, 11]), { 13: [1, 117], 42: $Vq }, o($V0, [2, 10]), o($VH, [2, 65]), o($V0, [2, 18]), { 18: 118, 19: [1, 119], 51: 50, 52: 51, 56: $Vm }, { 14: 120, 40: $VB, 50: $VC, 70: $VD }, { 19: [1, 121] }, o($V0, [2, 21]), o($V0, [2, 9]), o($V0, [2, 19])],\n    defaultActions: { 52: [2, 62], 72: [2, 57] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            this.begin(\"acc_title\");\n            return 24;\n            break;\n          case 1:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 2:\n            this.begin(\"acc_descr\");\n            return 26;\n            break;\n          case 3:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 4:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 5:\n            this.popState();\n            break;\n          case 6:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 7:\n            return 33;\n            break;\n          case 8:\n            return 34;\n            break;\n          case 9:\n            return 35;\n            break;\n          case 10:\n            return 36;\n            break;\n          case 11:\n            return 10;\n            break;\n          case 12:\n            break;\n          case 13:\n            return 8;\n            break;\n          case 14:\n            return 50;\n            break;\n          case 15:\n            return 70;\n            break;\n          case 16:\n            return 4;\n            break;\n          case 17:\n            this.begin(\"block\");\n            return 17;\n            break;\n          case 18:\n            return 49;\n            break;\n          case 19:\n            return 49;\n            break;\n          case 20:\n            return 42;\n            break;\n          case 21:\n            return 15;\n            break;\n          case 22:\n            return 13;\n            break;\n          case 23:\n            break;\n          case 24:\n            return 59;\n            break;\n          case 25:\n            return 56;\n            break;\n          case 26:\n            return 56;\n            break;\n          case 27:\n            return 60;\n            break;\n          case 28:\n            break;\n          case 29:\n            this.popState();\n            return 19;\n            break;\n          case 30:\n            return yy_.yytext[0];\n            break;\n          case 31:\n            return 20;\n            break;\n          case 32:\n            return 21;\n            break;\n          case 33:\n            this.begin(\"style\");\n            return 44;\n            break;\n          case 34:\n            this.popState();\n            return 10;\n            break;\n          case 35:\n            break;\n          case 36:\n            return 13;\n            break;\n          case 37:\n            return 42;\n            break;\n          case 38:\n            return 49;\n            break;\n          case 39:\n            this.begin(\"style\");\n            return 37;\n            break;\n          case 40:\n            return 43;\n            break;\n          case 41:\n            return 63;\n            break;\n          case 42:\n            return 65;\n            break;\n          case 43:\n            return 65;\n            break;\n          case 44:\n            return 65;\n            break;\n          case 45:\n            return 63;\n            break;\n          case 46:\n            return 63;\n            break;\n          case 47:\n            return 64;\n            break;\n          case 48:\n            return 64;\n            break;\n          case 49:\n            return 64;\n            break;\n          case 50:\n            return 64;\n            break;\n          case 51:\n            return 64;\n            break;\n          case 52:\n            return 65;\n            break;\n          case 53:\n            return 64;\n            break;\n          case 54:\n            return 65;\n            break;\n          case 55:\n            return 66;\n            break;\n          case 56:\n            return 66;\n            break;\n          case 57:\n            return 66;\n            break;\n          case 58:\n            return 66;\n            break;\n          case 59:\n            return 63;\n            break;\n          case 60:\n            return 64;\n            break;\n          case 61:\n            return 65;\n            break;\n          case 62:\n            return 67;\n            break;\n          case 63:\n            return 68;\n            break;\n          case 64:\n            return 69;\n            break;\n          case 65:\n            return 69;\n            break;\n          case 66:\n            return 68;\n            break;\n          case 67:\n            return 68;\n            break;\n          case 68:\n            return 68;\n            break;\n          case 69:\n            return 41;\n            break;\n          case 70:\n            return 47;\n            break;\n          case 71:\n            return 40;\n            break;\n          case 72:\n            return 48;\n            break;\n          case 73:\n            return yy_.yytext[0];\n            break;\n          case 74:\n            return 6;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:.*direction\\s+TB[^\\n]*)/i, /^(?:.*direction\\s+BT[^\\n]*)/i, /^(?:.*direction\\s+RL[^\\n]*)/i, /^(?:.*direction\\s+LR[^\\n]*)/i, /^(?:[\\n]+)/i, /^(?:\\s+)/i, /^(?:[\\s]+)/i, /^(?:\"[^\"%\\r\\n\\v\\b\\\\]+\")/i, /^(?:\"[^\"]*\")/i, /^(?:erDiagram\\b)/i, /^(?:\\{)/i, /^(?:#)/i, /^(?:#)/i, /^(?:,)/i, /^(?::::)/i, /^(?::)/i, /^(?:\\s+)/i, /^(?:\\b((?:PK)|(?:FK)|(?:UK))\\b)/i, /^(?:([^\\s]*)[~].*[~]([^\\s]*))/i, /^(?:([\\*A-Za-z_\\u00C0-\\uFFFF][A-Za-z0-9\\-\\_\\[\\]\\(\\)\\u00C0-\\uFFFF\\*]*))/i, /^(?:\"[^\"]*\")/i, /^(?:[\\n]+)/i, /^(?:\\})/i, /^(?:.)/i, /^(?:\\[)/i, /^(?:\\])/i, /^(?:style\\b)/i, /^(?:[\\n]+)/i, /^(?:\\s+)/i, /^(?::)/i, /^(?:,)/i, /^(?:#)/i, /^(?:classDef\\b)/i, /^(?:class\\b)/i, /^(?:one or zero\\b)/i, /^(?:one or more\\b)/i, /^(?:one or many\\b)/i, /^(?:1\\+)/i, /^(?:\\|o\\b)/i, /^(?:zero or one\\b)/i, /^(?:zero or more\\b)/i, /^(?:zero or many\\b)/i, /^(?:0\\+)/i, /^(?:\\}o\\b)/i, /^(?:many\\(0\\))/i, /^(?:many\\(1\\))/i, /^(?:many\\b)/i, /^(?:\\}\\|)/i, /^(?:one\\b)/i, /^(?:only one\\b)/i, /^(?:1\\b)/i, /^(?:\\|\\|)/i, /^(?:o\\|)/i, /^(?:o\\{)/i, /^(?:\\|\\{)/i, /^(?:\\s*u\\b)/i, /^(?:\\.\\.)/i, /^(?:--)/i, /^(?:to\\b)/i, /^(?:optionally to\\b)/i, /^(?:\\.-)/i, /^(?:-\\.)/i, /^(?:([^\\x00-\\x7F]|\\w|-|\\*)+)/i, /^(?:;)/i, /^(?:([^\\x00-\\x7F]|\\w|-|\\*)+)/i, /^(?:[0-9])/i, /^(?:.)/i, /^(?:$)/i],\n      conditions: { \"style\": { \"rules\": [34, 35, 36, 37, 38, 69, 70], \"inclusive\": false }, \"acc_descr_multiline\": { \"rules\": [5, 6], \"inclusive\": false }, \"acc_descr\": { \"rules\": [3], \"inclusive\": false }, \"acc_title\": { \"rules\": [1], \"inclusive\": false }, \"block\": { \"rules\": [23, 24, 25, 26, 27, 28, 29, 30], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 2, 4, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 31, 32, 33, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 71, 72, 73, 74], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar erDiagram_default = parser;\n\n// src/diagrams/er/erDb.ts\nvar ErDB = class {\n  constructor() {\n    this.entities = /* @__PURE__ */ new Map();\n    this.relationships = [];\n    this.classes = /* @__PURE__ */ new Map();\n    this.direction = \"TB\";\n    this.Cardinality = {\n      ZERO_OR_ONE: \"ZERO_OR_ONE\",\n      ZERO_OR_MORE: \"ZERO_OR_MORE\",\n      ONE_OR_MORE: \"ONE_OR_MORE\",\n      ONLY_ONE: \"ONLY_ONE\",\n      MD_PARENT: \"MD_PARENT\"\n    };\n    this.Identification = {\n      NON_IDENTIFYING: \"NON_IDENTIFYING\",\n      IDENTIFYING: \"IDENTIFYING\"\n    };\n    this.setAccTitle = setAccTitle;\n    this.getAccTitle = getAccTitle;\n    this.setAccDescription = setAccDescription;\n    this.getAccDescription = getAccDescription;\n    this.setDiagramTitle = setDiagramTitle;\n    this.getDiagramTitle = getDiagramTitle;\n    this.getConfig = /* @__PURE__ */ __name(() => getConfig().er, \"getConfig\");\n    this.clear();\n    this.addEntity = this.addEntity.bind(this);\n    this.addAttributes = this.addAttributes.bind(this);\n    this.addRelationship = this.addRelationship.bind(this);\n    this.setDirection = this.setDirection.bind(this);\n    this.addCssStyles = this.addCssStyles.bind(this);\n    this.addClass = this.addClass.bind(this);\n    this.setClass = this.setClass.bind(this);\n    this.setAccTitle = this.setAccTitle.bind(this);\n    this.setAccDescription = this.setAccDescription.bind(this);\n  }\n  static {\n    __name(this, \"ErDB\");\n  }\n  /**\n   * Add entity\n   * @param name - The name of the entity\n   * @param alias - The alias of the entity\n   */\n  addEntity(name, alias = \"\") {\n    if (!this.entities.has(name)) {\n      this.entities.set(name, {\n        id: `entity-${name}-${this.entities.size}`,\n        label: name,\n        attributes: [],\n        alias,\n        shape: \"erBox\",\n        look: getConfig().look ?? \"default\",\n        cssClasses: \"default\",\n        cssStyles: []\n      });\n      log.info(\"Added new entity :\", name);\n    } else if (!this.entities.get(name)?.alias && alias) {\n      this.entities.get(name).alias = alias;\n      log.info(`Add alias '${alias}' to entity '${name}'`);\n    }\n    return this.entities.get(name);\n  }\n  getEntity(name) {\n    return this.entities.get(name);\n  }\n  getEntities() {\n    return this.entities;\n  }\n  getClasses() {\n    return this.classes;\n  }\n  addAttributes(entityName, attribs) {\n    const entity = this.addEntity(entityName);\n    let i;\n    for (i = attribs.length - 1; i >= 0; i--) {\n      if (!attribs[i].keys) {\n        attribs[i].keys = [];\n      }\n      if (!attribs[i].comment) {\n        attribs[i].comment = \"\";\n      }\n      entity.attributes.push(attribs[i]);\n      log.debug(\"Added attribute \", attribs[i].name);\n    }\n  }\n  /**\n   * Add a relationship\n   *\n   * @param entA - The first entity in the relationship\n   * @param rolA - The role played by the first entity in relation to the second\n   * @param entB - The second entity in the relationship\n   * @param rSpec - The details of the relationship between the two entities\n   */\n  addRelationship(entA, rolA, entB, rSpec) {\n    const entityA = this.entities.get(entA);\n    const entityB = this.entities.get(entB);\n    if (!entityA || !entityB) {\n      return;\n    }\n    const rel = {\n      entityA: entityA.id,\n      roleA: rolA,\n      entityB: entityB.id,\n      relSpec: rSpec\n    };\n    this.relationships.push(rel);\n    log.debug(\"Added new relationship :\", rel);\n  }\n  getRelationships() {\n    return this.relationships;\n  }\n  getDirection() {\n    return this.direction;\n  }\n  setDirection(dir) {\n    this.direction = dir;\n  }\n  getCompiledStyles(classDefs) {\n    let compiledStyles = [];\n    for (const customClass of classDefs) {\n      const cssClass = this.classes.get(customClass);\n      if (cssClass?.styles) {\n        compiledStyles = [...compiledStyles, ...cssClass.styles ?? []].map((s) => s.trim());\n      }\n      if (cssClass?.textStyles) {\n        compiledStyles = [...compiledStyles, ...cssClass.textStyles ?? []].map((s) => s.trim());\n      }\n    }\n    return compiledStyles;\n  }\n  addCssStyles(ids, styles) {\n    for (const id of ids) {\n      const entity = this.entities.get(id);\n      if (!styles || !entity) {\n        return;\n      }\n      for (const style of styles) {\n        entity.cssStyles.push(style);\n      }\n    }\n  }\n  addClass(ids, style) {\n    ids.forEach((id) => {\n      let classNode = this.classes.get(id);\n      if (classNode === void 0) {\n        classNode = { id, styles: [], textStyles: [] };\n        this.classes.set(id, classNode);\n      }\n      if (style) {\n        style.forEach(function(s) {\n          if (/color/.exec(s)) {\n            const newStyle = s.replace(\"fill\", \"bgFill\");\n            classNode.textStyles.push(newStyle);\n          }\n          classNode.styles.push(s);\n        });\n      }\n    });\n  }\n  setClass(ids, classNames) {\n    for (const id of ids) {\n      const entity = this.entities.get(id);\n      if (entity) {\n        for (const className of classNames) {\n          entity.cssClasses += \" \" + className;\n        }\n      }\n    }\n  }\n  clear() {\n    this.entities = /* @__PURE__ */ new Map();\n    this.classes = /* @__PURE__ */ new Map();\n    this.relationships = [];\n    clear();\n  }\n  getData() {\n    const nodes = [];\n    const edges = [];\n    const config = getConfig();\n    for (const entityKey of this.entities.keys()) {\n      const entityNode = this.entities.get(entityKey);\n      if (entityNode) {\n        entityNode.cssCompiledStyles = this.getCompiledStyles(entityNode.cssClasses.split(\" \"));\n        nodes.push(entityNode);\n      }\n    }\n    let count = 0;\n    for (const relationship of this.relationships) {\n      const edge = {\n        id: getEdgeId(relationship.entityA, relationship.entityB, {\n          prefix: \"id\",\n          counter: count++\n        }),\n        type: \"normal\",\n        curve: \"basis\",\n        start: relationship.entityA,\n        end: relationship.entityB,\n        label: relationship.roleA,\n        labelpos: \"c\",\n        thickness: \"normal\",\n        classes: \"relationshipLine\",\n        arrowTypeStart: relationship.relSpec.cardB.toLowerCase(),\n        arrowTypeEnd: relationship.relSpec.cardA.toLowerCase(),\n        pattern: relationship.relSpec.relType == \"IDENTIFYING\" ? \"solid\" : \"dashed\",\n        look: config.look\n      };\n      edges.push(edge);\n    }\n    return { nodes, edges, other: {}, config, direction: \"TB\" };\n  }\n};\n\n// src/diagrams/er/erRenderer-unified.ts\nvar erRenderer_unified_exports = {};\n__export(erRenderer_unified_exports, {\n  draw: () => draw\n});\nimport { select } from \"d3\";\nvar draw = /* @__PURE__ */ __name(async function(text, id, _version, diag) {\n  log.info(\"REF0:\");\n  log.info(\"Drawing er diagram (unified)\", id);\n  const { securityLevel, er: conf, layout } = getConfig();\n  const data4Layout = diag.db.getData();\n  const svg = getDiagramElement(id, securityLevel);\n  data4Layout.type = diag.type;\n  data4Layout.layoutAlgorithm = getRegisteredLayoutAlgorithm(layout);\n  data4Layout.config.flowchart.nodeSpacing = conf?.nodeSpacing || 140;\n  data4Layout.config.flowchart.rankSpacing = conf?.rankSpacing || 80;\n  data4Layout.direction = diag.db.getDirection();\n  data4Layout.markers = [\"only_one\", \"zero_or_one\", \"one_or_more\", \"zero_or_more\"];\n  data4Layout.diagramId = id;\n  await render(data4Layout, svg);\n  if (data4Layout.layoutAlgorithm === \"elk\") {\n    svg.select(\".edges\").lower();\n  }\n  const backgroundNodes = svg.selectAll('[id*=\"-background\"]');\n  if (Array.from(backgroundNodes).length > 0) {\n    backgroundNodes.each(function() {\n      const backgroundNode = select(this);\n      const backgroundId = backgroundNode.attr(\"id\");\n      const nonBackgroundId = backgroundId.replace(\"-background\", \"\");\n      const nonBackgroundNode = svg.select(`#${CSS.escape(nonBackgroundId)}`);\n      if (!nonBackgroundNode.empty()) {\n        const transform = nonBackgroundNode.attr(\"transform\");\n        backgroundNode.attr(\"transform\", transform);\n      }\n    });\n  }\n  const padding = 8;\n  utils_default.insertTitle(\n    svg,\n    \"erDiagramTitleText\",\n    conf?.titleTopMargin ?? 25,\n    diag.db.getDiagramTitle()\n  );\n  setupViewPortForSVG(svg, padding, \"erDiagram\", conf?.useMaxWidth ?? true);\n}, \"draw\");\n\n// src/diagrams/er/styles.ts\nimport * as khroma from \"khroma\";\nvar fade = /* @__PURE__ */ __name((color, opacity) => {\n  const channel2 = khroma.channel;\n  const r = channel2(color, \"r\");\n  const g = channel2(color, \"g\");\n  const b = channel2(color, \"b\");\n  return khroma.rgba(r, g, b, opacity);\n}, \"fade\");\nvar getStyles = /* @__PURE__ */ __name((options) => `\n  .entityBox {\n    fill: ${options.mainBkg};\n    stroke: ${options.nodeBorder};\n  }\n\n  .relationshipLabelBox {\n    fill: ${options.tertiaryColor};\n    opacity: 0.7;\n    background-color: ${options.tertiaryColor};\n      rect {\n        opacity: 0.5;\n      }\n  }\n\n  .labelBkg {\n    background-color: ${fade(options.tertiaryColor, 0.5)};\n  }\n\n  .edgeLabel .label {\n    fill: ${options.nodeBorder};\n    font-size: 14px;\n  }\n\n  .label {\n    font-family: ${options.fontFamily};\n    color: ${options.nodeTextColor || options.textColor};\n  }\n\n  .edge-pattern-dashed {\n    stroke-dasharray: 8,8;\n  }\n\n  .node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon\n  {\n    fill: ${options.mainBkg};\n    stroke: ${options.nodeBorder};\n    stroke-width: 1px;\n  }\n\n  .relationshipLine {\n    stroke: ${options.lineColor};\n    stroke-width: 1;\n    fill: none;\n  }\n\n  .marker {\n    fill: none !important;\n    stroke: ${options.lineColor} !important;\n    stroke-width: 1;\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/er/erDiagram.ts\nvar diagram = {\n  parser: erDiagram_default,\n  get db() {\n    return new ErDB();\n  },\n  renderer: erRenderer_unified_exports,\n  styles: styles_default\n};\nexport {\n  diagram\n};\n"], "names": [], "sourceRoot": ""}