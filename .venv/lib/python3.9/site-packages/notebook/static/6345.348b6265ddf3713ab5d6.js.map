{"version": 3, "file": "6345.348b6265ddf3713ab5d6.js?v=348b6265ddf3713ab5d6", "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACmD;AACG;AACK;AACqC;AACvD;AACzC;AACA;AACA;AACA;AACA;AACA,eAAe,4DAAO;AACtB;AACA;AACA;AACA,gBAAgB,WAAW;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD,MAAM;AAC3D,aAAa;AACb,SAAS;AACT,0BAA0B,kCAAkC;AAC5D,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,gEAAe;AAC9B,eAAe,8EAAmB;AAClC;AACA;AACA;AACA,wBAAwB,6DAAU;AAClC,2GAA2G,oFAAyB;AACpI;AACA,oBAAoB,iBAAiB;AACrC;AACA,2BAA2B,uDAAI;AAC/B;AACA;AACA;AACA;AACA;AACA,wBAAwB,yDAAM;AAC9B;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,iEAAe,OAAO,EAAC", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/console-extension/lib/index.js"], "sourcesContent": ["// Copyright (c) Jupyter Development Team.\n// Distributed under the terms of the Modified BSD License.\nimport { IRouter, } from '@jupyterlab/application';\nimport { IConsoleTracker } from '@jupyterlab/console';\nimport { PageConfig, URLExt } from '@jupyterlab/coreutils';\nimport { INotebookPathOpener, defaultNotebookPathOpener, } from '@jupyter-notebook/application';\nimport { find } from '@lumino/algorithm';\n/**\n * A plugin to open consoles in a new tab\n */\nconst opener = {\n    id: '@jupyter-notebook/console-extension:opener',\n    requires: [IRouter],\n    autoStart: true,\n    description: 'A plugin to open consoles in a new tab',\n    activate: (app, router) => {\n        const { commands } = app;\n        const consolePattern = new RegExp('/consoles/(.*)');\n        const command = 'router:console';\n        commands.addCommand(command, {\n            execute: (args) => {\n                const parsed = args;\n                const matches = parsed.path.match(consolePattern);\n                if (!matches) {\n                    return;\n                }\n                const [, match] = matches;\n                if (!match) {\n                    return;\n                }\n                const path = decodeURIComponent(match);\n                commands.execute('console:create', { path });\n            },\n        });\n        router.register({ command, pattern: consolePattern });\n    },\n};\n/**\n * Open consoles in a new tab.\n */\nconst redirect = {\n    id: '@jupyter-notebook/console-extension:redirect',\n    requires: [IConsoleTracker],\n    optional: [INotebookPathOpener],\n    autoStart: true,\n    description: 'Open consoles in a new tab',\n    activate: (app, tracker, notebookPathOpener) => {\n        const baseUrl = PageConfig.getBaseUrl();\n        const opener = notebookPathOpener !== null && notebookPathOpener !== void 0 ? notebookPathOpener : defaultNotebookPathOpener;\n        tracker.widgetAdded.connect(async (send, console) => {\n            const { sessionContext } = console;\n            await sessionContext.ready;\n            const widget = find(app.shell.widgets('main'), (w) => w.id === console.id);\n            if (widget) {\n                // bail if the console is already added to the main area\n                return;\n            }\n            opener.open({\n                prefix: URLExt.join(baseUrl, 'consoles'),\n                path: sessionContext.path,\n                target: '_blank',\n            });\n            // the widget is not needed anymore\n            console.dispose();\n        });\n    },\n};\n/**\n * Export the plugins as default.\n */\nconst plugins = [opener, redirect];\nexport default plugins;\n"], "names": [], "sourceRoot": ""}