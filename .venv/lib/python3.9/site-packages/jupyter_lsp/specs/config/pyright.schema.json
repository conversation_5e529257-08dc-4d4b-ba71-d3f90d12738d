{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Pyright Language Server Configuration", "description": "Pyright Configuration Schema. Distributed under MIT License, Copyright (c) Microsoft Corporation.", "allowComments": true, "allowTrailingCommas": true, "type": "object", "properties": {"python.analysis.autoImportCompletions": {"type": "boolean", "default": true, "description": "Offer auto-import completions.", "scope": "resource"}, "python.analysis.autoSearchPaths": {"type": "boolean", "default": true, "description": "Automatically add common search paths like 'src'?", "scope": "resource"}, "python.analysis.extraPaths": {"type": "array", "default": [], "items": {"type": "string"}, "description": "Additional import search resolution paths", "scope": "resource"}, "python.analysis.stubPath": {"type": "string", "default": "typings", "description": "Path to directory containing custom type stub files.", "scope": "resource"}, "python.analysis.diagnosticMode": {"type": "string", "default": "openFilesOnly", "enum": ["openFilesOnly", "workspace"], "enumDescriptions": ["Analyzes and reports errors on only open files.", "Analyzes and reports errors on all files in the workspace."], "scope": "resource"}, "python.analysis.diagnosticSeverityOverrides": {"type": "object", "description": "Allows a user to override the severity levels for individual diagnostics.", "scope": "resource", "properties": {"reportGeneralTypeIssues": {"type": "string", "description": "Diagnostics for general type inconsistencies, unsupported operations, argument/parameter mismatches, etc. Covers all of the basic type-checking rules not covered by other rules. Does not include syntax errors.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportPropertyTypeMismatch": {"type": "string", "description": "Diagnostics for property whose setter and getter have mismatched types.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportFunctionMemberAccess": {"type": "string", "description": "Diagnostics for member accesses on functions.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportMissingImports": {"type": "string", "description": "Diagnostics for imports that have no corresponding imported python file or type stub file.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportMissingModuleSource": {"type": "string", "description": "Diagnostics for imports that have no corresponding source file. This happens when a type stub is found, but the module source file was not found, indicating that the code may fail at runtime when using this execution environment. Type checking will be done using the type stub.", "default": "warning", "enum": ["none", "information", "warning", "error"]}, "reportInvalidTypeForm": {"type": "string", "description": "Diagnostics for type expression that uses an invalid form.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportMissingTypeStubs": {"type": "string", "description": "Diagnostics for imports that have no corresponding type stub file (either a typeshed file or a custom type stub). The type checker requires type stubs to do its best job at analysis.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportImportCycles": {"type": "string", "description": "Diagnostics for cyclical import chains. These are not errors in Python, but they do slow down type analysis and often hint at architectural layering issues. Generally, they should be avoided.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportUnusedImport": {"type": "string", "description": "Diagnostics for an imported symbol that is not referenced within that file.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportUnusedClass": {"type": "string", "description": "Diagnostics for a class with a private name (starting with an underscore) that is not accessed.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportUnusedFunction": {"type": "string", "description": "Diagnostics for a function or method with a private name (starting with an underscore) that is not accessed.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportUnusedVariable": {"type": "string", "description": "Diagnostics for a variable that is not accessed.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportDuplicateImport": {"type": "string", "description": "Diagnostics for an imported symbol or module that is imported more than once.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportWildcardImportFromLibrary": {"type": "string", "description": "Diagnostics for an wildcard import from an external library.", "default": "warning", "enum": ["none", "information", "warning", "error"]}, "reportAbstractUsage": {"type": "string", "description": "Diagnostics for an attempt to instantiate an abstract or protocol class or use an abstract method.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportArgumentType": {"type": "string", "description": "Diagnostics for a type incompatibility for an argument to a call.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportAssertTypeFailure": {"type": "string", "description": "Diagnostics for a type incompatibility detected by a typing.assert_type call.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportAssignmentType": {"type": "string", "description": "Diagnostics for type incompatibilities for assignments.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportAttributeAccessIssue": {"type": "string", "description": "Diagnostics for issues involving attribute accesses.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportCallIssue": {"type": "string", "description": "Diagnostics for issues involving call expressions and arguments.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportInconsistentOverload": {"type": "string", "description": "Diagnostics for inconsistencies between function overload signatures and implementation.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportIndexIssue": {"type": "string", "description": "Diagnostics related to index operations and expressions.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportInvalidTypeArguments": {"type": "string", "description": "Diagnostics for invalid type argument usage.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportNoOverloadImplementation": {"type": "string", "description": "Diagnostics for an overloaded function or method with a missing implementation.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportOperatorIssue": {"type": "string", "description": "Diagnostics for related to unary or binary operators.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportOptionalSubscript": {"type": "string", "description": "Diagnostics for an attempt to subscript (index) a variable with an Optional type.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportOptionalMemberAccess": {"type": "string", "description": "Diagnostics for an attempt to access a member of a variable with an Optional type.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportOptionalCall": {"type": "string", "description": "Diagnostics for an attempt to call a variable with an Optional type.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportOptionalIterable": {"type": "string", "description": "Diagnostics for an attempt to use an Optional type as an iterable value (e.g. within a for statement).", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportOptionalContextManager": {"type": "string", "description": "Diagnostics for an attempt to use an Optional type as a context manager (as a parameter to a with statement).", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportOptionalOperand": {"type": "string", "description": "Diagnostics for an attempt to use an Optional type as an operand to a binary or unary operator (like '+', '==', 'or', 'not').", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportRedeclaration": {"type": "string", "description": "Diagnostics for an attempt to declare the type of a symbol multiple times.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportReturnType": {"type": "string", "description": "Diagnostics related to function return type compatibility.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportTypedDictNotRequiredAccess": {"type": "string", "description": "Diagnostics for an attempt to access a non-required key within a TypedDict without a check for its presence.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportUntypedFunctionDecorator": {"type": "string", "description": "Diagnostics for function decorators that have no type annotations. These obscure the function type, defeating many type analysis features.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportUntypedClassDecorator": {"type": "string", "description": "Diagnostics for class decorators that have no type annotations. These obscure the class type, defeating many type analysis features.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportUntypedBaseClass": {"type": "string", "description": "Diagnostics for base classes whose type cannot be determined statically. These obscure the class type, defeating many type analysis features.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportUntypedNamedTuple": {"type": "string", "description": "Diagnostics when “namedtuple” is used rather than “NamedTuple”. The former contains no type information, whereas the latter does.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportPrivateUsage": {"type": "string", "description": "Diagnostics for incorrect usage of private or protected variables or functions. Protected class members begin with a single underscore _ and can be accessed only by subclasses. Private class members begin with a double underscore but do not end in a double underscore and can be accessed only within the declaring class. Variables and functions declared outside of a class are considered private if their names start with either a single or double underscore, and they cannot be accessed outside of the declaring module.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportTypeCommentUsage": {"type": "string", "description": "Diagnostics for usage of deprecated type comments.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportPrivateImportUsage": {"type": "string", "description": "Diagnostics for incorrect usage of symbol imported from a \"py.typed\" module that is not re-exported from that module.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportConstantRedefinition": {"type": "string", "description": "Diagnostics for attempts to redefine variables whose names are all-caps with underscores and numerals.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportDeprecated": {"type": "string", "description": "Diagnostics for use of deprecated classes or functions.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportIncompatibleMethodOverride": {"type": "string", "description": "Diagnostics for methods that override a method of the same name in a base class in an incompatible manner (wrong number of parameters, incompatible parameter types, or incompatible return type).", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportIncompatibleVariableOverride": {"type": "string", "description": "Diagnostics for overrides in subclasses that redefine a variable in an incompatible way.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportInconsistentConstructor": {"type": "string", "description": "Diagnostics for __init__ and __new__ methods whose signatures are inconsistent.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportOverlappingOverload": {"type": "string", "description": "Diagnostics for function overloads that overlap in signature and obscure each other or have incompatible return types.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportPossiblyUnboundVariable": {"type": "string", "description": "Diagnostics for the use of variables that may be unbound on some code paths.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportMissingSuperCall": {"type": "string", "description": "Diagnostics for missing call to parent class for inherited `__init__` methods.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportUninitializedInstanceVariable": {"type": "string", "description": "Diagnostics for instance variables that are not declared or initialized within class body or `__init__` method.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportInvalidStringEscapeSequence": {"type": "string", "description": "Diagnostics for invalid escape sequences used within string literals. The Python specification indicates that such sequences will generate a syntax error in future versions.", "default": "warning", "enum": ["none", "information", "warning", "error"]}, "reportUnknownParameterType": {"type": "string", "description": "Diagnostics for input or return parameters for functions or methods that have an unknown type.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportUnknownArgumentType": {"type": "string", "description": "Diagnostics for call arguments for functions or methods that have an unknown type.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportUnknownLambdaType": {"type": "string", "description": "Diagnostics for input or return parameters for lambdas that have an unknown type.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportUnknownVariableType": {"type": "string", "description": "Diagnostics for variables that have an unknown type..", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportUnknownMemberType": {"type": "string", "description": "Diagnostics for class or instance variables that have an unknown type.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportMissingParameterType": {"type": "string", "description": "Diagnostics for parameters that are missing a type annotation.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportMissingTypeArgument": {"type": "string", "description": "Diagnostics for generic class reference with missing type arguments.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportInvalidTypeVarUse": {"type": "string", "description": "Diagnostics for improper use of type variables in a function signature.", "default": "warning", "enum": ["none", "information", "warning", "error"]}, "reportCallInDefaultInitializer": {"type": "string", "description": "Diagnostics for function calls within a default value initialization expression. Such calls can mask expensive operations that are performed at module initialization time.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportUnnecessaryIsInstance": {"type": "string", "description": "Diagnostics for 'isinstance' or 'issubclass' calls where the result is statically determined to be always true. Such calls are often indicative of a programming error.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportUnnecessaryCast": {"type": "string", "description": "Diagnostics for 'cast' calls that are statically determined to be unnecessary. Such calls are sometimes indicative of a programming error.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportUnnecessaryComparison": {"type": "string", "description": "Diagnostics for '==' and '!=' comparisons that are statically determined to be unnecessary. Such calls are sometimes indicative of a programming error.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportUnnecessaryContains": {"type": "string", "description": "Diagnostics for 'in' operation that is statically determined to be unnecessary. Such operations are sometimes indicative of a programming error.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportAssertAlwaysTrue": {"type": "string", "description": "Diagnostics for 'assert' statement that will provably always assert. This can be indicative of a programming error.", "default": "warning", "enum": ["none", "information", "warning", "error"]}, "reportSelfClsParameterName": {"type": "string", "description": "Diagnostics for a missing or misnamed “self” parameter in instance methods and “cls” parameter in class methods. Instance methods in metaclasses (classes that derive from “type”) are allowed to use “cls” for instance methods.", "default": "warning", "enum": ["none", "information", "warning", "error"]}, "reportImplicitStringConcatenation": {"type": "string", "description": "Diagnostics for two or more string literals that follow each other, indicating an implicit concatenation. This is considered a bad practice and often masks bugs such as missing commas.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportInvalidStubStatement": {"type": "string", "description": "Diagnostics for type stub statements that do not conform to PEP 484.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportIncompleteStub": {"type": "string", "description": "Diagnostics for the use of a module-level “__getattr__” function, indicating that the stub is incomplete.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportUndefinedVariable": {"type": "string", "description": "Diagnostics for undefined variables.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportUnboundVariable": {"type": "string", "description": "Diagnostics for unbound and possibly unbound variables.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportUnsupportedDunderAll": {"type": "string", "description": "Diagnostics for unsupported operations performed on __all__.", "default": "warning", "enum": ["none", "information", "warning", "error"]}, "reportUnusedCallResult": {"type": "string", "description": "Diagnostics for call expressions whose results are not consumed and are not None.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportUnusedCoroutine": {"type": "string", "description": "Diagnostics for call expressions that return a Coroutine and whose results are not consumed.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportUnusedExcept": {"type": "string", "description": "Diagnostics for unreachable except clause.", "default": "error", "enum": ["none", "information", "warning", "error"]}, "reportUnusedExpression": {"type": "string", "description": "Diagnostics for simple expressions whose value is not used in any way.", "default": "warning", "enum": ["none", "information", "warning", "error"]}, "reportUnnecessaryTypeIgnoreComment": {"type": "string", "description": "Diagnostics for '# type: ignore' comments that have no effect.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportMatchNotExhaustive": {"type": "string", "description": "Diagnostics for 'match' statements that do not exhaustively match all possible values.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportShadowedImports": {"type": "string", "description": "Diagnostics for files that are overriding a module in the stdlib.", "default": "none", "enum": ["none", "information", "warning", "error"]}, "reportImplicitOverride": {"type": "string", "description": "Diagnostics for overridden methods that do not include an `@override` decorator.", "default": "none", "enum": ["none", "information", "warning", "error"]}}}, "python.analysis.logLevel": {"type": "string", "default": "Information", "description": "Specifies the level of logging for the Output panel", "enum": ["Error", "Warning", "Information", "Trace"]}, "python.analysis.typeCheckingMode": {"type": "string", "default": "basic", "enum": ["off", "basic", "strict"], "description": "Defines the default rule set for type checking.", "scope": "resource"}, "python.analysis.typeshedPaths": {"type": "array", "default": [], "items": {"type": "string"}, "description": "Paths to look for typeshed modules.", "scope": "resource"}, "python.analysis.useLibraryCodeForTypes": {"type": "boolean", "default": false, "description": "Use library implementations to extract type information when type stub is not present.", "scope": "resource"}, "pyright.disableLanguageServices": {"type": "boolean", "default": false, "description": "Disables type completion, definitions, and references.", "scope": "resource"}, "pyright.disableTaggedHints": {"type": "boolean", "default": false, "description": "Disable hint diagnostics with special hints for grayed-out or strike-through text.", "scope": "resource"}, "pyright.disableOrganizeImports": {"type": "boolean", "default": false, "description": "Disables the “Organize Imports” command.", "scope": "resource"}, "python.pythonPath": {"type": "string", "default": "python", "description": "Path to Python, you can use a custom version of Python.", "scope": "resource"}, "python.venvPath": {"type": "string", "default": "", "description": "Path to folder with a list of Virtual Environments.", "scope": "resource"}}}