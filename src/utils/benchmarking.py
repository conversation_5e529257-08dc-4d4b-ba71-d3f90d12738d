"""
Benchmarking utilities for measuring performance across different data science libraries.
"""

import time
import psutil
import gc
from contextlib import contextmanager
from typing import Dict, Any, Callable
import pandas as pd
from memory_profiler import memory_usage
import logging

logger = logging.getLogger(__name__)

class PerformanceMetrics:
    """Container for performance metrics."""
    
    def __init__(self):
        self.execution_time = 0.0
        self.peak_memory_mb = 0.0
        self.memory_delta_mb = 0.0
        self.cpu_percent = 0.0
        self.success = True
        self.error_message = None
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            'execution_time_seconds': self.execution_time,
            'peak_memory_mb': self.peak_memory_mb,
            'memory_delta_mb': self.memory_delta_mb,
            'cpu_percent': self.cpu_percent,
            'success': self.success,
            'error_message': self.error_message
        }

@contextmanager
def measure_performance():
    """Context manager to measure execution time, memory usage, and CPU utilization."""
    # Force garbage collection before measurement
    gc.collect()
    
    # Get initial memory usage
    process = psutil.Process()
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    metrics = PerformanceMetrics()
    
    # Start timing
    start_time = time.perf_counter()
    start_cpu_times = process.cpu_times()
    
    try:
        yield metrics
        metrics.success = True
    except Exception as e:
        metrics.success = False
        metrics.error_message = str(e)
        logger.error(f"Benchmark failed: {e}")
    finally:
        # Calculate execution time
        end_time = time.perf_counter()
        metrics.execution_time = end_time - start_time
        
        # Calculate memory usage
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        metrics.memory_delta_mb = final_memory - initial_memory
        
        # Get peak memory (this is approximate)
        metrics.peak_memory_mb = max(initial_memory, final_memory)
        
        # Calculate CPU usage (approximate)
        end_cpu_times = process.cpu_times()
        cpu_time_used = (end_cpu_times.user - start_cpu_times.user + 
                        end_cpu_times.system - start_cpu_times.system)
        if metrics.execution_time > 0:
            metrics.cpu_percent = (cpu_time_used / metrics.execution_time) * 100
        
        # Force garbage collection after measurement
        gc.collect()

def benchmark_function(func: Callable, *args, **kwargs) -> PerformanceMetrics:
    """Benchmark a single function call."""
    with measure_performance() as metrics:
        result = func(*args, **kwargs)
        # Store result in metrics for potential later use
        metrics.result = result
    
    return metrics

def benchmark_memory_intensive(func: Callable, *args, **kwargs) -> PerformanceMetrics:
    """Benchmark a function with detailed memory profiling."""
    metrics = PerformanceMetrics()
    
    def wrapper():
        try:
            start_time = time.perf_counter()
            result = func(*args, **kwargs)
            end_time = time.perf_counter()
            metrics.execution_time = end_time - start_time
            metrics.success = True
            metrics.result = result
            return result
        except Exception as e:
            metrics.success = False
            metrics.error_message = str(e)
            raise
    
    # Use memory_profiler for more accurate memory measurement
    try:
        mem_usage = memory_usage(wrapper, interval=0.1, timeout=None)
        if mem_usage:
            metrics.peak_memory_mb = max(mem_usage)
            metrics.memory_delta_mb = max(mem_usage) - min(mem_usage)
    except Exception as e:
        logger.warning(f"Memory profiling failed: {e}")
        # Fallback to basic measurement
        with measure_performance() as fallback_metrics:
            wrapper()
        metrics = fallback_metrics
    
    return metrics

class BenchmarkSuite:
    """A suite of benchmarks for comparing different implementations."""
    
    def __init__(self, name: str):
        self.name = name
        self.results = []
    
    def add_benchmark(self, 
                     implementation_name: str,
                     func: Callable,
                     *args,
                     description: str = "",
                     use_memory_profiler: bool = False,
                     **kwargs) -> PerformanceMetrics:
        """Add and run a benchmark."""
        logger.info(f"Running benchmark: {implementation_name}")
        
        if use_memory_profiler:
            metrics = benchmark_memory_intensive(func, *args, **kwargs)
        else:
            metrics = benchmark_function(func, *args, **kwargs)
        
        result_record = {
            'suite_name': self.name,
            'implementation': implementation_name,
            'description': description,
            'timestamp': pd.Timestamp.now(),
            **metrics.to_dict()
        }
        
        self.results.append(result_record)
        
        if metrics.success:
            logger.info(f"  ✓ {implementation_name}: {metrics.execution_time:.3f}s, "
                       f"{metrics.peak_memory_mb:.1f}MB peak memory")
        else:
            logger.error(f"  ✗ {implementation_name}: FAILED - {metrics.error_message}")
        
        return metrics
    
    def get_results_df(self) -> pd.DataFrame:
        """Get benchmark results as a pandas DataFrame."""
        return pd.DataFrame(self.results)
    
    def save_results(self, filepath: str):
        """Save benchmark results to a file."""
        df = self.get_results_df()
        if filepath.endswith('.csv'):
            df.to_csv(filepath, index=False)
        elif filepath.endswith('.parquet'):
            df.to_parquet(filepath, index=False)
        else:
            # Default to CSV
            df.to_csv(f"{filepath}.csv", index=False)
        
        logger.info(f"Benchmark results saved to {filepath}")
    
    def print_summary(self):
        """Print a summary of benchmark results."""
        if not self.results:
            print("No benchmark results available.")
            return
        
        df = self.get_results_df()
        successful_results = df[df['success'] == True]
        
        if successful_results.empty:
            print("All benchmarks failed.")
            return
        
        print(f"\n{'='*60}")
        print(f"BENCHMARK RESULTS: {self.name}")
        print(f"{'='*60}")
        
        # Sort by execution time
        sorted_results = successful_results.sort_values('execution_time_seconds')
        
        print(f"{'Implementation':<20} {'Time (s)':<10} {'Memory (MB)':<12} {'CPU %':<8}")
        print("-" * 60)
        
        for _, row in sorted_results.iterrows():
            print(f"{row['implementation']:<20} "
                  f"{row['execution_time_seconds']:<10.3f} "
                  f"{row['peak_memory_mb']:<12.1f} "
                  f"{row['cpu_percent']:<8.1f}")
        
        # Show failed benchmarks
        failed_results = df[df['success'] == False]
        if not failed_results.empty:
            print(f"\nFailed benchmarks:")
            for _, row in failed_results.iterrows():
                print(f"  ✗ {row['implementation']}: {row['error_message']}")

def compare_implementations(name: str, implementations: Dict[str, Callable], 
                          *args, **kwargs) -> BenchmarkSuite:
    """Quick utility to compare multiple implementations of the same operation."""
    suite = BenchmarkSuite(name)
    
    for impl_name, impl_func in implementations.items():
        suite.add_benchmark(impl_name, impl_func, *args, **kwargs)
    
    return suite
