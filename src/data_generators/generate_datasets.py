#!/usr/bin/env python3
"""
Data Generation Script for Modern Data Science Technologies Demo

This script generates synthetic datasets of various sizes for benchmarking
Apache Arrow, Polars, and DuckDB performance.
"""

import os
import sys
import numpy as np
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from datetime import datetime, timedelta
from pathlib import Path
import argparse
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatasetGenerator:
    """Generate synthetic datasets for benchmarking."""
    
    def __init__(self, output_dir: str = "data"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Dataset configurations
        self.sizes = {
            'small': 100_000,      # ~10MB
            'medium': 1_000_000,   # ~100MB  
            'large': 10_000_000,   # ~1GB
            'xlarge': 100_000_000  # ~10GB
        }
    
    def generate_sales_data(self, n_rows: int, seed: int = 42) -> pd.DataFrame:
        """Generate synthetic sales transaction data."""
        np.random.seed(seed)
        
        # Date range for the last 2 years
        start_date = datetime.now() - timedelta(days=730)
        dates = pd.date_range(start_date, periods=n_rows, freq='1min')
        
        # Product categories and regions
        categories = ['Electronics', 'Clothing', 'Books', 'Home', 'Sports', 'Beauty', 'Toys']
        regions = ['North', 'South', 'East', 'West', 'Central']
        payment_methods = ['Credit Card', 'Debit Card', 'Cash', 'Digital Wallet']
        
        data = {
            'transaction_id': [f'TXN_{i:08d}' for i in range(n_rows)],
            'timestamp': np.random.choice(dates, n_rows),
            'customer_id': np.random.randint(1, min(n_rows // 10, 100000), n_rows),
            'product_category': np.random.choice(categories, n_rows),
            'region': np.random.choice(regions, n_rows),
            'payment_method': np.random.choice(payment_methods, n_rows),
            'amount': np.round(np.random.lognormal(3, 1, n_rows), 2),
            'quantity': np.random.randint(1, 10, n_rows),
            'discount_percent': np.random.choice([0, 5, 10, 15, 20, 25], n_rows, p=[0.4, 0.2, 0.15, 0.15, 0.05, 0.05]),
            'is_weekend': np.random.choice([True, False], n_rows, p=[0.3, 0.7]),
            'customer_age': np.random.randint(18, 80, n_rows),
            'rating': np.random.choice([1, 2, 3, 4, 5], n_rows, p=[0.05, 0.1, 0.2, 0.35, 0.3])
        }
        
        df = pd.DataFrame(data)
        
        # Calculate derived columns
        df['discounted_amount'] = df['amount'] * (1 - df['discount_percent'] / 100)
        df['total_amount'] = df['discounted_amount'] * df['quantity']
        
        return df
    
    def generate_time_series_data(self, n_rows: int, seed: int = 42) -> pd.DataFrame:
        """Generate synthetic time series data for sensor readings."""
        np.random.seed(seed)
        
        # Generate timestamps at regular intervals
        start_time = datetime.now() - timedelta(days=365)
        timestamps = pd.date_range(start_time, periods=n_rows, freq='1min')
        
        # Simulate multiple sensors
        n_sensors = min(100, max(10, n_rows // 10000))
        sensor_ids = [f'SENSOR_{i:03d}' for i in range(n_sensors)]
        
        data = []
        for i in range(n_rows):
            sensor_id = np.random.choice(sensor_ids)
            base_temp = 20 + 10 * np.sin(2 * np.pi * i / (24 * 60))  # Daily cycle
            
            row = {
                'timestamp': timestamps[i],
                'sensor_id': sensor_id,
                'temperature': base_temp + np.random.normal(0, 2),
                'humidity': max(0, min(100, 50 + np.random.normal(0, 15))),
                'pressure': 1013.25 + np.random.normal(0, 10),
                'wind_speed': max(0, np.random.exponential(5)),
                'wind_direction': np.random.uniform(0, 360),
                'battery_level': max(0, min(100, 100 - (i % 10000) / 100 + np.random.normal(0, 5)))
            }
            data.append(row)
        
        return pd.DataFrame(data)
    
    def save_dataset(self, df: pd.DataFrame, name: str, size: str):
        """Save dataset in multiple formats."""
        base_path = self.output_dir / size
        base_path.mkdir(exist_ok=True)
        
        # Save as CSV
        csv_path = base_path / f"{name}.csv"
        df.to_csv(csv_path, index=False)
        logger.info(f"Saved CSV: {csv_path}")
        
        # Save as Parquet
        parquet_path = base_path / f"{name}.parquet"
        df.to_parquet(parquet_path, index=False)
        logger.info(f"Saved Parquet: {parquet_path}")
        
        # Save as Arrow IPC
        arrow_path = base_path / f"{name}.arrow"
        table = pa.Table.from_pandas(df)
        with pa.ipc.new_file(arrow_path, table.schema) as writer:
            writer.write_table(table)
        logger.info(f"Saved Arrow IPC: {arrow_path}")
        
        return {
            'csv': csv_path,
            'parquet': parquet_path,
            'arrow': arrow_path,
            'rows': len(df),
            'memory_mb': df.memory_usage(deep=True).sum() / 1024 / 1024
        }
    
    def generate_all_datasets(self, sizes_to_generate=None):
        """Generate all datasets for all specified sizes."""
        if sizes_to_generate is None:
            sizes_to_generate = list(self.sizes.keys())
        
        results = {}
        
        for size_name in sizes_to_generate:
            if size_name not in self.sizes:
                logger.warning(f"Unknown size: {size_name}")
                continue
                
            n_rows = self.sizes[size_name]
            logger.info(f"Generating {size_name} datasets ({n_rows:,} rows)")
            
            results[size_name] = {}
            
            # Generate sales data
            logger.info(f"Generating sales data...")
            sales_df = self.generate_sales_data(n_rows)
            results[size_name]['sales'] = self.save_dataset(sales_df, 'sales', size_name)
            
            # Generate time series data
            logger.info(f"Generating time series data...")
            ts_df = self.generate_time_series_data(n_rows)
            results[size_name]['timeseries'] = self.save_dataset(ts_df, 'timeseries', size_name)
            
            logger.info(f"Completed {size_name} datasets")
        
        return results

def main():
    parser = argparse.ArgumentParser(description='Generate synthetic datasets for benchmarking')
    parser.add_argument('--sizes', nargs='+', 
                       choices=['small', 'medium', 'large', 'xlarge'],
                       default=['small', 'medium'],
                       help='Dataset sizes to generate')
    parser.add_argument('--output-dir', default='data',
                       help='Output directory for datasets')
    
    args = parser.parse_args()
    
    generator = DatasetGenerator(args.output_dir)
    results = generator.generate_all_datasets(args.sizes)
    
    # Print summary
    print("\n" + "="*60)
    print("DATASET GENERATION SUMMARY")
    print("="*60)
    
    for size, datasets in results.items():
        print(f"\n{size.upper()} datasets:")
        for dataset_name, info in datasets.items():
            print(f"  {dataset_name}:")
            print(f"    Rows: {info['rows']:,}")
            print(f"    Memory: {info['memory_mb']:.1f} MB")
            print(f"    Files: CSV, Parquet, Arrow IPC")

if __name__ == "__main__":
    main()
