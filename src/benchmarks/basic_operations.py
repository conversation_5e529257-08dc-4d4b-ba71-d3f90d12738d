#!/usr/bin/env python3
"""
Basic Operations Benchmark

Compares Apache Arrow, Polars, and DuckDB for fundamental data operations:
- Data loading from different formats
- Basic filtering and selection
- Simple aggregations
- Memory usage patterns
"""

import sys
import os
from pathlib import Path
import argparse
import logging

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

import pandas as pd
import polars as pl
import pyarrow as pa
import pyarrow.parquet as pq
import pyarrow.compute as pc
import duckdb
from utils.benchmarking import BenchmarkSuite, compare_implementations

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BasicOperationsBenchmark:
    """Benchmark basic data operations across different libraries."""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.results = {}
    
    def benchmark_data_loading(self, size: str = "small", dataset: str = "sales"):
        """Benchmark data loading from different formats."""
        suite = BenchmarkSuite(f"Data Loading - {size} {dataset}")
        
        # File paths
        csv_path = self.data_dir / size / f"{dataset}.csv"
        parquet_path = self.data_dir / size / f"{dataset}.parquet"
        arrow_path = self.data_dir / size / f"{dataset}.arrow"
        
        if not csv_path.exists():
            logger.error(f"Data files not found. Please run data generation first.")
            return suite
        
        # Pandas implementations
        def pandas_read_csv():
            return pd.read_csv(csv_path)
        
        def pandas_read_parquet():
            return pd.read_parquet(parquet_path)
        
        # Polars implementations
        def polars_read_csv():
            return pl.read_csv(csv_path)
        
        def polars_read_parquet():
            return pl.read_parquet(parquet_path)
        
        # Arrow implementations
        def arrow_read_csv():
            return pa.csv.read_csv(csv_path)
        
        def arrow_read_parquet():
            return pq.read_table(parquet_path)
        
        def arrow_read_ipc():
            with pa.ipc.open_file(arrow_path) as reader:
                return reader.read_all()
        
        # DuckDB implementations
        def duckdb_read_csv():
            conn = duckdb.connect()
            return conn.execute(f"SELECT * FROM read_csv_auto('{csv_path}')").fetchdf()
        
        def duckdb_read_parquet():
            conn = duckdb.connect()
            return conn.execute(f"SELECT * FROM read_parquet('{parquet_path}')").fetchdf()
        
        # Run benchmarks
        suite.add_benchmark("Pandas CSV", pandas_read_csv, use_memory_profiler=True)
        suite.add_benchmark("Pandas Parquet", pandas_read_parquet, use_memory_profiler=True)
        suite.add_benchmark("Polars CSV", polars_read_csv, use_memory_profiler=True)
        suite.add_benchmark("Polars Parquet", polars_read_parquet, use_memory_profiler=True)
        suite.add_benchmark("Arrow CSV", arrow_read_csv, use_memory_profiler=True)
        suite.add_benchmark("Arrow Parquet", arrow_read_parquet, use_memory_profiler=True)
        suite.add_benchmark("Arrow IPC", arrow_read_ipc, use_memory_profiler=True)
        suite.add_benchmark("DuckDB CSV", duckdb_read_csv, use_memory_profiler=True)
        suite.add_benchmark("DuckDB Parquet", duckdb_read_parquet, use_memory_profiler=True)
        
        return suite
    
    def benchmark_filtering(self, size: str = "small", dataset: str = "sales"):
        """Benchmark filtering operations."""
        suite = BenchmarkSuite(f"Filtering - {size} {dataset}")
        
        # Load data once for each library
        parquet_path = self.data_dir / size / f"{dataset}.parquet"
        
        if not parquet_path.exists():
            logger.error(f"Data file not found: {parquet_path}")
            return suite
        
        # Load data
        df_pandas = pd.read_parquet(parquet_path)
        df_polars = pl.read_parquet(parquet_path)
        table_arrow = pq.read_table(parquet_path)
        
        # Setup DuckDB
        conn = duckdb.connect()
        conn.execute(f"CREATE TABLE sales AS SELECT * FROM read_parquet('{parquet_path}')")
        
        # Define filtering operations
        if dataset == "sales":
            # Filter for high-value transactions
            def pandas_filter():
                return df_pandas[df_pandas['total_amount'] > 100]
            
            def polars_filter():
                return df_polars.filter(pl.col('total_amount') > 100)
            
            def arrow_filter():
                return pc.filter(table_arrow, pc.greater(table_arrow['total_amount'], 100))
            
            def duckdb_filter():
                return conn.execute("SELECT * FROM sales WHERE total_amount > 100").fetchdf()
        
        else:  # timeseries
            # Filter for high temperature readings
            def pandas_filter():
                return df_pandas[df_pandas['temperature'] > 25]
            
            def polars_filter():
                return df_polars.filter(pl.col('temperature') > 25)
            
            def arrow_filter():
                return pc.filter(table_arrow, pc.greater(table_arrow['temperature'], 25))
            
            def duckdb_filter():
                return conn.execute("SELECT * FROM sales WHERE temperature > 25").fetchdf()
        
        # Run benchmarks
        suite.add_benchmark("Pandas", pandas_filter)
        suite.add_benchmark("Polars", polars_filter)
        suite.add_benchmark("Arrow", arrow_filter)
        suite.add_benchmark("DuckDB", duckdb_filter)
        
        conn.close()
        return suite
    
    def benchmark_aggregation(self, size: str = "small", dataset: str = "sales"):
        """Benchmark aggregation operations."""
        suite = BenchmarkSuite(f"Aggregation - {size} {dataset}")
        
        # Load data
        parquet_path = self.data_dir / size / f"{dataset}.parquet"
        
        if not parquet_path.exists():
            logger.error(f"Data file not found: {parquet_path}")
            return suite
        
        df_pandas = pd.read_parquet(parquet_path)
        df_polars = pl.read_parquet(parquet_path)
        table_arrow = pq.read_table(parquet_path)
        
        # Setup DuckDB
        conn = duckdb.connect()
        conn.execute(f"CREATE TABLE data AS SELECT * FROM read_parquet('{parquet_path}')")
        
        # Define aggregation operations
        if dataset == "sales":
            def pandas_agg():
                return df_pandas.groupby('product_category').agg({
                    'total_amount': ['sum', 'mean', 'count'],
                    'quantity': 'sum'
                })
            
            def polars_agg():
                return df_polars.group_by('product_category').agg([
                    pl.col('total_amount').sum().alias('total_amount_sum'),
                    pl.col('total_amount').mean().alias('total_amount_mean'),
                    pl.col('total_amount').count().alias('total_amount_count'),
                    pl.col('quantity').sum().alias('quantity_sum')
                ])
            
            def arrow_agg():
                # Arrow compute is more limited for groupby operations
                return table_arrow.group_by('product_category').aggregate([
                    ('total_amount', 'sum'),
                    ('total_amount', 'mean'),
                    ('quantity', 'sum')
                ])
            
            def duckdb_agg():
                return conn.execute("""
                    SELECT 
                        product_category,
                        SUM(total_amount) as total_amount_sum,
                        AVG(total_amount) as total_amount_mean,
                        COUNT(total_amount) as total_amount_count,
                        SUM(quantity) as quantity_sum
                    FROM data 
                    GROUP BY product_category
                """).fetchdf()
        
        else:  # timeseries
            def pandas_agg():
                return df_pandas.groupby('sensor_id').agg({
                    'temperature': ['mean', 'min', 'max'],
                    'humidity': 'mean'
                })
            
            def polars_agg():
                return df_polars.group_by('sensor_id').agg([
                    pl.col('temperature').mean().alias('temp_mean'),
                    pl.col('temperature').min().alias('temp_min'),
                    pl.col('temperature').max().alias('temp_max'),
                    pl.col('humidity').mean().alias('humidity_mean')
                ])
            
            def arrow_agg():
                return table_arrow.group_by('sensor_id').aggregate([
                    ('temperature', 'mean'),
                    ('temperature', 'min'),
                    ('temperature', 'max'),
                    ('humidity', 'mean')
                ])
            
            def duckdb_agg():
                return conn.execute("""
                    SELECT 
                        sensor_id,
                        AVG(temperature) as temp_mean,
                        MIN(temperature) as temp_min,
                        MAX(temperature) as temp_max,
                        AVG(humidity) as humidity_mean
                    FROM data 
                    GROUP BY sensor_id
                """).fetchdf()
        
        # Run benchmarks
        suite.add_benchmark("Pandas", pandas_agg)
        suite.add_benchmark("Polars", polars_agg)
        # Note: Arrow groupby is experimental, might skip
        try:
            suite.add_benchmark("Arrow", arrow_agg)
        except Exception as e:
            logger.warning(f"Arrow aggregation failed: {e}")
        
        suite.add_benchmark("DuckDB", duckdb_agg)
        
        conn.close()
        return suite
    
    def run_all_benchmarks(self, sizes=None, datasets=None):
        """Run all basic operation benchmarks."""
        if sizes is None:
            sizes = ["small", "medium"]
        if datasets is None:
            datasets = ["sales", "timeseries"]
        
        all_results = []
        
        for size in sizes:
            for dataset in datasets:
                logger.info(f"\n{'='*60}")
                logger.info(f"Running benchmarks for {size} {dataset} dataset")
                logger.info(f"{'='*60}")
                
                # Data loading benchmark
                loading_suite = self.benchmark_data_loading(size, dataset)
                loading_suite.print_summary()
                all_results.extend(loading_suite.results)
                
                # Filtering benchmark
                filtering_suite = self.benchmark_filtering(size, dataset)
                filtering_suite.print_summary()
                all_results.extend(filtering_suite.results)
                
                # Aggregation benchmark
                agg_suite = self.benchmark_aggregation(size, dataset)
                agg_suite.print_summary()
                all_results.extend(agg_suite.results)
        
        # Save combined results
        results_df = pd.DataFrame(all_results)
        results_path = Path("results") / "basic_operations_benchmark.csv"
        results_path.parent.mkdir(exist_ok=True)
        results_df.to_csv(results_path, index=False)
        logger.info(f"\nAll results saved to {results_path}")
        
        return results_df

def main():
    parser = argparse.ArgumentParser(description='Run basic operations benchmarks')
    parser.add_argument('--sizes', nargs='+', 
                       choices=['small', 'medium', 'large', 'xlarge'],
                       default=['small', 'medium'],
                       help='Dataset sizes to benchmark')
    parser.add_argument('--datasets', nargs='+',
                       choices=['sales', 'timeseries'],
                       default=['sales', 'timeseries'],
                       help='Datasets to benchmark')
    parser.add_argument('--data-dir', default='data',
                       help='Directory containing datasets')
    
    args = parser.parse_args()
    
    benchmark = BasicOperationsBenchmark(args.data_dir)
    benchmark.run_all_benchmarks(args.sizes, args.datasets)

if __name__ == "__main__":
    main()
